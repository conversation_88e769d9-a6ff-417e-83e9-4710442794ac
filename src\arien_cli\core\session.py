"""
Session management for Arien CLI.

This module handles chat sessions, message history, and conversation state.
"""

import asyncio
import json
import logging
import uuid
from dataclasses import dataclass, field
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Optional, Union

from .config import config_manager
from .llm_providers.base import Chat<PERSON>essage, LLMProvider, MessageRole
from .llm_providers.deepseek import DeepseekProvider
from .llm_providers.ollama import OllamaProvider
from .tools.tool_registry import tool_registry

logger = logging.getLogger(__name__)


@dataclass
class SessionMessage:
    """A message in a chat session."""
    id: str
    role: MessageRole
    content: str
    timestamp: datetime
    tool_calls: Optional[List[Dict[str, Any]]] = None
    tool_results: Optional[List[Dict[str, Any]]] = None
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class SessionInfo:
    """Information about a chat session."""
    id: str
    name: str
    provider: str
    model: str
    created_at: datetime
    last_active: datetime
    message_count: int
    working_directory: str


class ChatSession:
    """
    Manages a single chat session with an LLM provider.
    
    Features:
    - Message history management
    - Tool calling integration
    - Session persistence
    - Provider switching
    - Context management
    """
    
    def __init__(
        self,
        session_id: Optional[str] = None,
        provider_name: str = "deepseek",
        model: str = "deepseek-chat",
        working_directory: Optional[str] = None
    ):
        """
        Initialize a chat session.
        
        Args:
            session_id: Unique session identifier
            provider_name: LLM provider name
            model: Model to use
            working_directory: Working directory for commands
        """
        self.id = session_id or str(uuid.uuid4())
        self.provider_name = provider_name
        self.model = model
        self.working_directory = working_directory or config_manager.config.default_session.working_directory
        
        self.messages: List[SessionMessage] = []
        self.provider: Optional[LLMProvider] = None
        self.created_at = datetime.now()
        self.last_active = datetime.now()
        
        # Session metadata
        self.name = f"Session {self.created_at.strftime('%Y-%m-%d %H:%M')}"
        self.system_prompt = config_manager.config.default_session.system_prompt
        
        # Initialize with system message
        if self.system_prompt:
            self.add_message(
                role=MessageRole.SYSTEM,
                content=self.system_prompt
            )
    
    async def initialize_provider(self) -> None:
        """Initialize the LLM provider."""
        config = config_manager.get_provider_config(self.provider_name)
        if not config:
            raise ValueError(f"Provider {self.provider_name} not configured")
        
        if self.provider_name == "deepseek":
            self.provider = DeepseekProvider(
                api_key=config.api_key,
                base_url=config.base_url
            )
        elif self.provider_name == "ollama":
            self.provider = OllamaProvider(
                base_url=config.base_url
            )
        else:
            raise ValueError(f"Unsupported provider: {self.provider_name}")
        
        await self.provider.initialize()
        logger.info(f"Initialized provider {self.provider_name} for session {self.id}")
    
    def add_message(
        self,
        role: MessageRole,
        content: str,
        tool_calls: Optional[List[Dict[str, Any]]] = None,
        tool_results: Optional[List[Dict[str, Any]]] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> SessionMessage:
        """
        Add a message to the session.
        
        Args:
            role: Message role
            content: Message content
            tool_calls: Tool calls made by assistant
            tool_results: Results from tool execution
            metadata: Additional metadata
            
        Returns:
            Created SessionMessage
        """
        message = SessionMessage(
            id=str(uuid.uuid4()),
            role=role,
            content=content,
            timestamp=datetime.now(),
            tool_calls=tool_calls,
            tool_results=tool_results,
            metadata=metadata or {}
        )
        
        self.messages.append(message)
        self.last_active = datetime.now()
        
        return message
    
    def get_chat_messages(self, include_system: bool = True) -> List[ChatMessage]:
        """
        Get messages in ChatMessage format for LLM.
        
        Args:
            include_system: Whether to include system messages
            
        Returns:
            List of ChatMessage objects
        """
        chat_messages = []
        
        for msg in self.messages:
            if not include_system and msg.role == MessageRole.SYSTEM:
                continue
            
            chat_messages.append(ChatMessage(
                role=msg.role,
                content=msg.content,
                tool_calls=msg.tool_calls
            ))
        
        return chat_messages
    
    async def send_message(
        self,
        content: str,
        stream: bool = True
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """
        Send a message and get streaming response.
        
        Args:
            content: User message content
            stream: Whether to stream the response
            
        Yields:
            Response chunks with type and content
        """
        if not self.provider:
            await self.initialize_provider()
        
        # Add user message
        self.add_message(MessageRole.USER, content)
        
        # Get chat messages for LLM
        chat_messages = self.get_chat_messages()
        
        # Get tool definitions
        tools = tool_registry.get_tool_definitions()
        
        try:
            # Generate response
            response = await self.provider.chat_completion(
                messages=chat_messages,
                model=self.model,
                tools=tools,
                stream=stream
            )
            
            if stream:
                async for chunk in self._handle_streaming_response(response):
                    yield chunk
            else:
                async for chunk in self._handle_non_streaming_response(response):
                    yield chunk
        
        except Exception as e:
            logger.error(f"Error in chat completion: {e}")
            yield {
                "type": "error",
                "content": f"Error: {str(e)}"
            }
    
    async def _handle_streaming_response(self, response) -> AsyncGenerator[Dict[str, Any], None]:
        """Handle streaming response from LLM."""
        accumulated_content = ""
        accumulated_tool_calls = []
        
        async for chunk in response:
            if chunk.content:
                accumulated_content += chunk.content
                yield {
                    "type": "content",
                    "content": chunk.content
                }
            
            if chunk.tool_calls:
                accumulated_tool_calls.extend(chunk.tool_calls)
                yield {
                    "type": "tool_calls",
                    "tool_calls": chunk.tool_calls
                }
            
            if chunk.finish_reason:
                # Add assistant message
                assistant_msg = self.add_message(
                    role=MessageRole.ASSISTANT,
                    content=accumulated_content,
                    tool_calls=[tc.__dict__ for tc in accumulated_tool_calls] if accumulated_tool_calls else None
                )
                
                # Execute tool calls if present
                if accumulated_tool_calls:
                    async for tool_result in self._execute_tool_calls(accumulated_tool_calls):
                        yield tool_result
                
                yield {
                    "type": "finish",
                    "finish_reason": chunk.finish_reason
                }
    
    async def _handle_non_streaming_response(self, response) -> AsyncGenerator[Dict[str, Any], None]:
        """Handle non-streaming response from LLM."""
        # Add assistant message
        assistant_msg = self.add_message(
            role=MessageRole.ASSISTANT,
            content=response.content,
            tool_calls=[tc.__dict__ for tc in response.tool_calls] if response.tool_calls else None
        )
        
        yield {
            "type": "content",
            "content": response.content
        }
        
        # Execute tool calls if present
        if response.tool_calls:
            async for tool_result in self._execute_tool_calls(response.tool_calls):
                yield tool_result
        
        yield {
            "type": "finish",
            "finish_reason": response.finish_reason
        }
    
    async def _execute_tool_calls(self, tool_calls) -> AsyncGenerator[Dict[str, Any], None]:
        """Execute tool calls and yield results."""
        for tool_call in tool_calls:
            yield {
                "type": "tool_execution",
                "tool_name": tool_call.name,
                "arguments": tool_call.arguments
            }
            
            try:
                result = await tool_registry.execute_tool(
                    tool_call.name,
                    tool_call.arguments
                )
                
                # Add tool result message
                self.add_message(
                    role=MessageRole.TOOL,
                    content=json.dumps(result, indent=2),
                    metadata={"tool_call_id": tool_call.id}
                )
                
                yield {
                    "type": "tool_result",
                    "tool_name": tool_call.name,
                    "result": result
                }
            
            except Exception as e:
                error_result = {
                    "success": False,
                    "error": str(e)
                }
                
                self.add_message(
                    role=MessageRole.TOOL,
                    content=json.dumps(error_result, indent=2),
                    metadata={"tool_call_id": tool_call.id, "error": True}
                )
                
                yield {
                    "type": "tool_error",
                    "tool_name": tool_call.name,
                    "error": str(e)
                }
    
    def get_session_info(self) -> SessionInfo:
        """Get session information."""
        return SessionInfo(
            id=self.id,
            name=self.name,
            provider=self.provider_name,
            model=self.model,
            created_at=self.created_at,
            last_active=self.last_active,
            message_count=len(self.messages),
            working_directory=self.working_directory
        )
    
    async def close(self) -> None:
        """Close the session and cleanup resources."""
        if self.provider:
            await self.provider.close()
        
        logger.info(f"Closed session {self.id}")


class SessionManager:
    """Manages multiple chat sessions."""
    
    def __init__(self):
        """Initialize session manager."""
        self.sessions: Dict[str, ChatSession] = {}
        self.active_session_id: Optional[str] = None
    
    def create_session(
        self,
        provider_name: str = "deepseek",
        model: str = "deepseek-chat",
        working_directory: Optional[str] = None
    ) -> ChatSession:
        """Create a new chat session."""
        session = ChatSession(
            provider_name=provider_name,
            model=model,
            working_directory=working_directory
        )
        
        self.sessions[session.id] = session
        self.active_session_id = session.id
        
        logger.info(f"Created new session {session.id}")
        return session
    
    def get_session(self, session_id: str) -> Optional[ChatSession]:
        """Get a session by ID."""
        return self.sessions.get(session_id)
    
    def get_active_session(self) -> Optional[ChatSession]:
        """Get the currently active session."""
        if self.active_session_id:
            return self.sessions.get(self.active_session_id)
        return None
    
    def set_active_session(self, session_id: str) -> bool:
        """Set the active session."""
        if session_id in self.sessions:
            self.active_session_id = session_id
            return True
        return False
    
    def list_sessions(self) -> List[SessionInfo]:
        """List all sessions."""
        return [session.get_session_info() for session in self.sessions.values()]
    
    async def close_session(self, session_id: str) -> bool:
        """Close and remove a session."""
        if session_id in self.sessions:
            session = self.sessions[session_id]
            await session.close()
            del self.sessions[session_id]
            
            if self.active_session_id == session_id:
                self.active_session_id = None
            
            return True
        return False


# Global session manager instance
session_manager = SessionManager()
