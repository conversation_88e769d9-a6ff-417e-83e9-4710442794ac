"""
Terminal layout component for Arien CLI.

This module provides the main terminal layout with responsive design
and structured components.
"""

import asyncio
from typing import Any, Dict, List, Optional

from rich.console import Console, Group
from rich.layout import Layout
from rich.panel import Panel
from rich.text import Text

from .header import HeaderComponent
from .chat_input import ChatInputComponent
from .chat_output import ChatOutputComponent
from .thinking_spinner import ThinkingSpinnerComponent


class TerminalLayoutComponent:
    """
    Main terminal layout component.
    
    Provides a responsive layout with:
    - Header with session info
    - Main chat area
    - Input area
    - Status indicators
    """
    
    def __init__(self, console: Optional[Console] = None):
        """
        Initialize terminal layout.
        
        Args:
            console: Rich console instance
        """
        self.console = console or Console()
        self.layout = Layout()
        
        # Initialize components
        self.header = HeaderComponent()
        self.chat_output = ChatOutputComponent()
        self.chat_input = ChatInputComponent()
        self.thinking_spinner = ThinkingSpinnerComponent()
        
        # Layout state
        self.is_thinking = False
        self.show_input = True
        
        self._setup_layout()
    
    def _setup_layout(self) -> None:
        """Setup the main layout structure."""
        # Create main layout sections
        self.layout.split_column(
            Layout(name="header", size=3),
            Layout(name="main"),
            Layout(name="input", size=3)
        )
        
        # Setup main area
        self.layout["main"].split_column(
            Layout(name="chat", ratio=1),
            Layout(name="status", size=1)
        )
        
        # Initial content
        self.layout["header"].update(self.header.render())
        self.layout["chat"].update(self.chat_output.render())
        self.layout["input"].update(self.chat_input.render())
        self.layout["status"].update(Panel("Ready", style="dim"))
    
    def update_header(
        self,
        session_id: Optional[str] = None,
        provider: Optional[str] = None,
        model: Optional[str] = None,
        working_directory: Optional[str] = None
    ) -> None:
        """
        Update header information.
        
        Args:
            session_id: Current session ID
            provider: Current provider
            model: Current model
            working_directory: Current working directory
        """
        self.header.update(
            session_id=session_id,
            provider=provider,
            model=model,
            working_directory=working_directory
        )
        self.layout["header"].update(self.header.render())
    
    def add_message(
        self,
        role: str,
        content: str,
        metadata: Optional[Dict[str, Any]] = None
    ) -> None:
        """
        Add a message to the chat output.
        
        Args:
            role: Message role (user, assistant, system, tool)
            content: Message content
            metadata: Additional metadata
        """
        self.chat_output.add_message(role, content, metadata)
        self.layout["chat"].update(self.chat_output.render())
    
    def add_tool_execution(
        self,
        tool_name: str,
        arguments: Dict[str, Any],
        result: Optional[Dict[str, Any]] = None
    ) -> None:
        """
        Add tool execution to the chat output.
        
        Args:
            tool_name: Name of the executed tool
            arguments: Tool arguments
            result: Tool execution result
        """
        self.chat_output.add_tool_execution(tool_name, arguments, result)
        self.layout["chat"].update(self.chat_output.render())
    
    def start_thinking(self, message: str = "Thinking...") -> None:
        """
        Start thinking animation.
        
        Args:
            message: Thinking message
        """
        self.is_thinking = True
        self.thinking_spinner.start(message)
        self.layout["status"].update(self.thinking_spinner.render())
    
    def stop_thinking(self) -> None:
        """Stop thinking animation."""
        self.is_thinking = False
        self.thinking_spinner.stop()
        self.layout["status"].update(Panel("Ready", style="dim"))
    
    def update_status(self, message: str, style: str = "dim") -> None:
        """
        Update status message.
        
        Args:
            message: Status message
            style: Rich style for the message
        """
        if not self.is_thinking:
            self.layout["status"].update(Panel(message, style=style))
    
    def set_input_prompt(self, prompt: str) -> None:
        """
        Set input prompt.
        
        Args:
            prompt: Input prompt text
        """
        self.chat_input.set_prompt(prompt)
        self.layout["input"].update(self.chat_input.render())
    
    def show_input_area(self, show: bool = True) -> None:
        """
        Show or hide input area.
        
        Args:
            show: Whether to show input area
        """
        self.show_input = show
        if show:
            self.layout["input"].size = 3
            self.layout["input"].update(self.chat_input.render())
        else:
            self.layout["input"].size = 0
            self.layout["input"].update("")
    
    def clear_chat(self) -> None:
        """Clear chat history."""
        self.chat_output.clear()
        self.layout["chat"].update(self.chat_output.render())
    
    def show_error(self, error: str, suggestions: Optional[List[str]] = None) -> None:
        """
        Show error message.
        
        Args:
            error: Error message
            suggestions: Recovery suggestions
        """
        self.chat_output.add_error(error, suggestions)
        self.layout["chat"].update(self.chat_output.render())
    
    def show_help(self, help_text: str) -> None:
        """
        Show help information.
        
        Args:
            help_text: Help text to display
        """
        self.chat_output.add_help(help_text)
        self.layout["chat"].update(self.chat_output.render())
    
    def render(self) -> Layout:
        """
        Render the complete layout.
        
        Returns:
            Rich Layout object
        """
        return self.layout
    
    def get_dimensions(self) -> Dict[str, int]:
        """
        Get current layout dimensions.
        
        Returns:
            Dictionary with width and height
        """
        try:
            size = self.console.size
            return {
                "width": size.width,
                "height": size.height,
                "header_height": 3,
                "input_height": 3 if self.show_input else 0,
                "chat_height": size.height - 3 - (3 if self.show_input else 0) - 1,
                "status_height": 1
            }
        except Exception:
            return {
                "width": 80,
                "height": 24,
                "header_height": 3,
                "input_height": 3 if self.show_input else 0,
                "chat_height": 17,
                "status_height": 1
            }
    
    def resize(self, width: int, height: int) -> None:
        """
        Handle terminal resize.
        
        Args:
            width: New terminal width
            height: New terminal height
        """
        # Update components with new dimensions
        self.chat_output.set_width(width)
        self.chat_input.set_width(width)
        
        # Refresh layout
        self.layout["header"].update(self.header.render())
        self.layout["chat"].update(self.chat_output.render())
        self.layout["input"].update(self.chat_input.render())
    
    def export_chat_history(self) -> List[Dict[str, Any]]:
        """
        Export chat history.
        
        Returns:
            List of message dictionaries
        """
        return self.chat_output.export_history()
    
    def import_chat_history(self, messages: List[Dict[str, Any]]) -> None:
        """
        Import chat history.
        
        Args:
            messages: List of message dictionaries
        """
        self.chat_output.import_history(messages)
        self.layout["chat"].update(self.chat_output.render())
