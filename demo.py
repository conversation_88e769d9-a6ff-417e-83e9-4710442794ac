#!/usr/bin/env python3
"""
Demo script for Arien CLI.

This script demonstrates the key features of the Arien CLI system.
"""

import asyncio
import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from arien_cli.core.session import session_manager, MessageRole
from arien_cli.core.tools.tool_registry import tool_registry
from arien_cli.ui.components.slash_commands import SlashCommandsComponent
from arien_cli.ui.components.tool_calls_processor import ToolCallsProcessor
from arien_cli.utils.output_formatter import output_formatter
from rich.console import Console
from rich.panel import Panel
from rich.text import Text

console = Console()


def print_header():
    """Print demo header."""
    header_text = Text()
    header_text.append("🚀 Arien CLI Demo\n", style="bold cyan")
    header_text.append("Modern AI-powered terminal assistant\n", style="white")
    header_text.append("Features: Shell execution, Safety checks, Real-time streaming", style="dim")
    
    console.print(Panel(header_text, title="Demo", border_style="cyan"))
    console.print()


async def demo_tool_execution():
    """Demonstrate tool execution."""
    console.print("[bold yellow]📋 Tool Execution Demo[/bold yellow]")
    console.print()
    
    # Show available tools
    tools = tool_registry.get_tool_definitions()
    console.print(f"Available tools: {len(tools)}")
    for tool in tools:
        console.print(f"  • {tool['function']['name']}")
    console.print()
    
    # Execute a simple command
    console.print("Executing: [cyan]echo 'Hello from Arien CLI!'[/cyan]")
    result = await tool_registry.execute_tool(
        "execute_shell_command",
        {"command": "echo 'Hello from Arien CLI!'"}
    )
    
    formatted_result = output_formatter.format_command_result(result)
    console.print(Panel(formatted_result, title="Command Result", border_style="green"))
    console.print()
    
    # Execute a file listing command
    console.print("Executing: [cyan]ls -la[/cyan] (or [cyan]dir[/cyan] on Windows)")
    import platform
    if platform.system() == "Windows":
        command = "dir"
    else:
        command = "ls -la"
    
    result = await tool_registry.execute_tool(
        "execute_shell_command",
        {"command": command}
    )
    
    formatted_result = output_formatter.format_command_result(result)
    console.print(Panel(formatted_result, title="Directory Listing", border_style="green"))
    console.print()


async def demo_slash_commands():
    """Demonstrate slash commands."""
    console.print("[bold yellow]⚡ Slash Commands Demo[/bold yellow]")
    console.print()
    
    slash_commands = SlashCommandsComponent(console)
    
    # Test help command
    console.print("Executing: [cyan]/help[/cyan]")
    result = await slash_commands.execute_command("/help")
    if result["success"]:
        console.print("✅ Help command executed successfully")
    console.print()
    
    # Test status command
    console.print("Executing: [cyan]/status[/cyan]")
    result = await slash_commands.execute_command("/status")
    if result["success"]:
        console.print(Panel(result["result"], title="System Status", border_style="blue"))
    console.print()


async def demo_session_management():
    """Demonstrate session management."""
    console.print("[bold yellow]💬 Session Management Demo[/bold yellow]")
    console.print()
    
    # Create a session
    session = session_manager.create_session(
        provider_name="deepseek",
        model="deepseek-chat"
    )
    
    console.print(f"Created session: [cyan]{session.id}[/cyan]")
    
    # Add some messages
    session.add_message(MessageRole.USER, "Hello, can you help me with file operations?")
    session.add_message(MessageRole.ASSISTANT, "Of course! I can help you with various file operations using shell commands. What would you like to do?")
    
    console.print(f"Session now has [green]{len(session.messages)}[/green] messages")
    
    # Show session info
    info = session.get_session_info()
    session_info = f"""Session Information:
ID: {info.id}
Provider: {info.provider}
Model: {info.model}
Messages: {info.message_count}
Working Directory: {info.working_directory}
Created: {info.created_at.strftime('%Y-%m-%d %H:%M:%S')}"""
    
    console.print(Panel(session_info, title="Session Info", border_style="magenta"))
    console.print()


async def demo_tool_processor():
    """Demonstrate tool calls processor."""
    console.print("[bold yellow]🔧 Tool Calls Processor Demo[/bold yellow]")
    console.print()
    
    processor = ToolCallsProcessor(console)
    
    # Simulate multiple tool calls
    tool_calls = [
        {
            "id": "call_1",
            "name": "execute_shell_command",
            "arguments": {"command": "echo 'First command'"}
        },
        {
            "id": "call_2", 
            "name": "execute_shell_command",
            "arguments": {"command": "echo 'Second command'"}
        }
    ]
    
    console.print("Processing multiple tool calls...")
    results = await processor.process_tool_calls(tool_calls, show_progress=True)
    
    console.print(f"✅ Processed {len(results)} tool calls")
    
    # Show execution summary
    summary = processor.get_execution_summary()
    summary_text = f"""Execution Summary:
Total Executions: {summary['total_executions']}
Success Rate: {summary['success_rate']}%
Average Time: {summary['average_time']}s"""
    
    console.print(Panel(summary_text, title="Execution Summary", border_style="yellow"))
    console.print()


async def demo_output_formatting():
    """Demonstrate output formatting."""
    console.print("[bold yellow]🎨 Output Formatting Demo[/bold yellow]")
    console.print()
    
    # Format different types of content
    
    # 1. Command result
    result = {
        "success": True,
        "command": "echo 'test'",
        "exit_code": 0,
        "stdout": "test",
        "stderr": "",
        "execution_time": 0.123
    }
    
    formatted = output_formatter.format_command_result(result)
    console.print("Command Result Formatting:")
    console.print(formatted)
    console.print()
    
    # 2. JSON formatting
    data = {
        "name": "Arien CLI",
        "version": "1.0.0",
        "features": ["AI assistance", "Shell execution", "Safety checks"],
        "config": {
            "providers": ["deepseek", "ollama"],
            "models": ["deepseek-chat", "deepseek-reasoner"]
        }
    }
    
    json_formatted = output_formatter.format_json(data)
    console.print("JSON Formatting:")
    console.print(Panel(json_formatted, title="JSON Data", border_style="blue"))
    console.print()
    
    # 3. Error formatting
    error_formatted = output_formatter.format_error(
        "Command execution failed",
        ["Check command syntax", "Verify file permissions", "Try with sudo"]
    )
    console.print("Error Formatting:")
    console.print(error_formatted)
    console.print()


async def main():
    """Run the demo."""
    print_header()
    
    try:
        await demo_tool_execution()
        await demo_slash_commands()
        await demo_session_management()
        await demo_tool_processor()
        await demo_output_formatting()
        
        # Final message
        final_text = Text()
        final_text.append("🎉 Demo completed successfully!\n", style="bold green")
        final_text.append("Arien CLI is ready for use.\n", style="white")
        final_text.append("Run 'python -m arien_cli.main chat' to start the interactive mode.", style="dim")
        
        console.print(Panel(final_text, title="Demo Complete", border_style="green"))
        
    except Exception as e:
        console.print(f"[red]Demo failed: {e}[/red]")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
